name: 'E2E Tests'

on:
  push:
    branches:
      - 'main'
      - 'feat/e2e/**'
  merge_group:

jobs:
  e2e-test-linux:
    name: 'E2E Test (Linux) - ${{ matrix.sandbox }}'
    runs-on: 'ubuntu-latest'
    strategy:
      matrix:
        sandbox:
          - 'sandbox:none'
          - 'sandbox:docker'
        node-version:
          - '20.x'
          - '22.x'
          - '24.x'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5

      - name: 'Set up Node.js ${{ matrix.node-version }}'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version: '${{ matrix.node-version }}'
          cache: 'npm'
          cache-dependency-path: 'package-lock.json'
          registry-url: 'https://registry.npmjs.org/'

      - name: 'Configure npm for rate limiting'
        run: |-
          npm config set fetch-retry-mintimeout 20000
          npm config set fetch-retry-maxtimeout 120000
          npm config set fetch-retries 5
          npm config set fetch-timeout 300000

      - name: 'Install dependencies'
        run: |-
          npm ci --prefer-offline --no-audit --progress=false

      - name: 'Build project'
        run: |-
          npm run build

      - name: 'Set up Docker'
        if: |-
          ${{ matrix.sandbox == 'sandbox:docker' }}
        uses: 'docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435' # ratchet:docker/setup-buildx-action@v3

      - name: 'Set up Podman'
        if: |-
          ${{ matrix.sandbox == 'sandbox:podman' }}
        uses: 'redhat-actions/podman-login@4934294ad0449894bcd1e9f191899d7292469603' # ratchet:redhat-actions/podman-login@v1
        with:
          registry: 'docker.io'
          username: '${{ secrets.DOCKERHUB_USERNAME }}'
          password: '${{ secrets.DOCKERHUB_TOKEN }}'

      - name: 'Run E2E tests'
        env:
          OPENAI_API_KEY: '${{ secrets.OPENAI_API_KEY }}'
          OPENAI_BASE_URL: '${{ secrets.OPENAI_BASE_URL }}'
          OPENAI_MODEL: '${{ secrets.OPENAI_MODEL }}'
          KEEP_OUTPUT: 'true'
          SANDBOX: '${{ matrix.sandbox }}'
          VERBOSE: 'true'
        run: |-
          npm run "test:integration:${SANDBOX}"

  e2e-test-macos:
    name: 'E2E Test - macOS'
    runs-on: 'macos-latest'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5

      - name: 'Set up Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
          cache-dependency-path: 'package-lock.json'
          registry-url: 'https://registry.npmjs.org/'

      - name: 'Configure npm for rate limiting'
        run: |-
          npm config set fetch-retry-mintimeout 20000
          npm config set fetch-retry-maxtimeout 120000
          npm config set fetch-retries 5
          npm config set fetch-timeout 300000

      - name: 'Install dependencies'
        run: |-
          npm ci --prefer-offline --no-audit --progress=false

      - name: 'Build project'
        run: |-
          npm run build

      - name: 'Run E2E tests'
        env:
          OPENAI_API_KEY: '${{ secrets.OPENAI_API_KEY }}'
          OPENAI_BASE_URL: '${{ secrets.OPENAI_BASE_URL }}'
          OPENAI_MODEL: '${{ secrets.OPENAI_MODEL }}'
        run: 'npm run test:e2e'
