name: 'Bug Report'
description: 'Report a bug to help us improve Qwen Code'
labels: ['kind/bug', 'status/need-triage']
body:
  - type: 'markdown'
    attributes:
      value: |-
        > [!IMPORTANT]
        > Thanks for taking the time to fill out this bug report!
        >
        > Please search **[existing issues](https://github.com/QwenLM/qwen-code/issues)** to see if an issue already exists for the bug you encountered.

  - type: 'textarea'
    id: 'problem'
    attributes:
      label: 'What happened?'
      description: 'A clear and concise description of what the bug is.'
    validations:
      required: true

  - type: 'textarea'
    id: 'expected'
    attributes:
      label: 'What did you expect to happen?'
    validations:
      required: true

  - type: 'textarea'
    id: 'info'
    attributes:
      label: 'Client information'
      description: 'Please paste the full text from the `/about` command run from Qwen Code. Also include which platform (macOS, Windows, Linux).'
      value: |
        <details>

        ```console
        $ qwen /about
        # paste output here
        ```

        </details>
    validations:
      required: true

  - type: 'textarea'
    id: 'login-info'
    attributes:
      label: 'Login information'
      description: 'Describe how you are logging in (e.g., API Config).'

  - type: 'textarea'
    id: 'additional-context'
    attributes:
      label: 'Anything else we need to know?'
      description: 'Add any other context about the problem here.'
