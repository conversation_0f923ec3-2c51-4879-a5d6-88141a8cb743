{"name": "qwen-code-vscode-ide-companion", "displayName": "<PERSON><PERSON>", "description": "Enable Qwen Code with direct access to your VS Code workspace.", "version": "0.0.10", "publisher": "qwen<PERSON>", "icon": "assets/icon.png", "repository": {"type": "git", "url": "https://github.com/QwenLM/qwen-code.git", "directory": "packages/vscode-ide-companion"}, "engines": {"vscode": "^1.99.0"}, "license": "LICENSE", "preview": true, "categories": ["AI"], "keywords": ["qwen-code", "qwen code", "qwen", "qwen code", "cli", "ide integration", "ide companion"], "activationEvents": ["onStartupFinished"], "contributes": {"languages": [{"id": "qwen-diff-editable"}], "commands": [{"command": "qwen.diff.accept", "title": "Qwen Code: Accept Current Diff", "icon": "$(check)"}, {"command": "qwen.diff.cancel", "title": "Qwen Code: Close Diff Editor", "icon": "$(close)"}, {"command": "qwen-code.runQwenCode", "title": "Qwen Code: Run"}, {"command": "qwen-code.showNotices", "title": "Qwen Code: View Third-Party Notices"}], "menus": {"commandPalette": [{"command": "qwen.diff.accept", "when": "qwen.diff.isVisible"}, {"command": "qwen.diff.cancel", "when": "qwen.diff.isVisible"}], "editor/title": [{"command": "qwen.diff.accept", "when": "qwen.diff.isVisible", "group": "navigation"}, {"command": "qwen.diff.cancel", "when": "qwen.diff.isVisible", "group": "navigation"}]}, "keybindings": [{"command": "qwen.diff.accept", "key": "ctrl+s", "when": "qwen.diff.isVisible"}, {"command": "qwen.diff.accept", "key": "cmd+s", "when": "qwen.diff.isVisible"}]}, "main": "./dist/extension.cjs", "type": "module", "scripts": {"vscode:prepublish": "npm run generate:notices && npm run check-types && npm run lint && node esbuild.js --production", "build": "npm run compile", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "vsce package --no-dependencies", "generate:notices": "node ./scripts/generate-notices.js", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vitest run", "test:ci": "vitest run --coverage"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "20.x", "@types/vscode": "^1.99.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1", "cors": "^2.8.5", "express": "^5.1.0", "zod": "^3.25.76"}}