# Qwen Code Companion

The Qwen Code Companion extension seamlessly integrates [Qwen Code](https://github.com/QwenLM/qwen-code). This extension is compatible with both VS Code and VS Code forks.

# Features

- Open Editor File Context: Qwen Code gains awareness of the files you have open in your editor, providing it with a richer understanding of your project's structure and content.

- Selection Context: Qwen Code can easily access your cursor's position and selected text within the editor, giving it valuable context directly from your current work.

- Native Diffing: Seamlessly view, modify, and accept code changes suggested by Qwen Code directly within the editor.

- Launch Qwen Code: Quickly start a new Qwen Code session from the Command Palette (Cmd+Shift+P or Ctrl+Shift+P) by running the "Qwen Code: Run" command.

# Requirements

To use this extension, you'll need:

- VS Code version 1.101.0 or newer
- Qwen Code (installed separately) running within the VS Code integrated terminal

# Terms of Service and Privacy Notice

By installing this extension, you agree to the [Terms of Service](https://github.com/QwenLM/qwen-code/blob/main/docs/tos-privacy.md).
