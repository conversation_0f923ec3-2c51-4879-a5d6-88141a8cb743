{"name": "@qwen-code/qwen-code-core", "version": "0.0.10", "description": "Qwen <PERSON> Core", "repository": {"type": "git", "url": "git+https://github.com/QwenLM/qwen-code.git"}, "type": "module", "main": "dist/index.js", "scripts": {"build": "node ../../scripts/build_package.js", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "test": "vitest run", "test:ci": "vitest run --coverage", "typecheck": "tsc --noEmit"}, "files": ["dist"], "dependencies": {"@google/genai": "1.13.0", "@modelcontextprotocol/sdk": "^1.11.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-logs-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-logs-otlp-http": "^0.203.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.203.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/instrumentation-http": "^0.203.0", "@opentelemetry/sdk-node": "^0.203.0", "@types/glob": "^8.1.0", "@types/html-to-text": "^9.0.4", "ajv": "^8.17.1", "chardet": "^2.1.0", "diff": "^7.0.0", "dotenv": "^17.1.0", "fdir": "^6.4.6", "fzf": "^0.5.2", "glob": "^10.4.5", "google-auth-library": "^9.11.0", "html-to-text": "^9.0.5", "https-proxy-agent": "^7.0.6", "ignore": "^7.0.0", "jsonrepair": "^3.13.0", "marked": "^15.0.12", "micromatch": "^4.0.8", "mnemonist": "^0.40.3", "open": "^10.1.2", "openai": "5.11.0", "picomatch": "^4.0.1", "shell-quote": "^1.8.3", "simple-git": "^3.28.0", "strip-ansi": "^7.1.0", "tiktoken": "^1.0.21", "undici": "^7.10.0", "uuid": "^9.0.1", "ws": "^8.18.0", "@xterm/headless": "5.5.0"}, "optionalDependencies": {"@lydell/node-pty": "1.1.0", "node-pty": "^1.0.0", "@lydell/node-pty-darwin-arm64": "1.1.0", "@lydell/node-pty-darwin-x64": "1.1.0", "@lydell/node-pty-linux-x64": "1.1.0", "@lydell/node-pty-win32-arm64": "1.1.0", "@lydell/node-pty-win32-x64": "1.1.0"}, "devDependencies": {"@qwen-code/qwen-code-test-utils": "file:../test-utils", "@types/diff": "^7.0.2", "@types/dotenv": "^6.1.1", "@types/micromatch": "^4.0.8", "@types/minimatch": "^5.1.2", "@types/picomatch": "^4.0.1", "@types/ws": "^8.5.10", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "engines": {"node": ">=20"}}