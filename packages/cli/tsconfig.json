{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "dist",
    "jsx": "react-jsx",
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": ["node", "vitest/globals"]
  },
  "include": [
    "index.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.json",
    "./package.json"
  ],
  "exclude": [
    "node_modules",
    "dist",
    // TODO(5691): Fix type errors and remove excludes.
    "src/commands/mcp.test.ts",
    "src/commands/mcp/add.test.ts",
    "src/commands/mcp/list.test.ts",
    "src/commands/mcp/remove.test.ts",
    "src/config/config.integration.test.ts",
    "src/config/config.test.ts",
    "src/config/extension.test.ts",
    "src/config/settings.test.ts",
    "src/nonInteractiveCli.test.ts",
    "src/services/FileCommandLoader.test.ts",
    "src/services/prompt-processors/argumentProcessor.test.ts",
    "src/utils/cleanup.test.ts",
    "src/utils/handleAutoUpdate.test.ts",
    "src/utils/startupWarnings.test.ts",
    "src/ui/App.test.tsx",
    "src/ui/commands/aboutCommand.test.ts",
    "src/ui/commands/authCommand.test.ts",
    "src/ui/commands/bugCommand.test.ts",
    "src/ui/commands/clearCommand.test.ts",
    "src/ui/commands/compressCommand.test.ts",
    "src/ui/commands/copyCommand.test.ts",
    "src/ui/commands/corgiCommand.test.ts",
    "src/ui/commands/docsCommand.test.ts",
    "src/ui/commands/editorCommand.test.ts",
    "src/ui/commands/extensionsCommand.test.ts",
    "src/ui/commands/helpCommand.test.ts",
    "src/ui/commands/restoreCommand.test.ts",
    "src/ui/commands/settingsCommand.test.ts",
    "src/ui/commands/themeCommand.test.ts",
    "src/ui/commands/chatCommand.test.ts",
    "src/ui/commands/directoryCommand.test.tsx",
    "src/ui/commands/ideCommand.test.ts",
    "src/ui/commands/initCommand.test.ts",
    "src/ui/commands/privacyCommand.test.ts",
    "src/ui/commands/quitCommand.test.ts",
    "src/ui/commands/mcpCommand.test.ts",
    "src/ui/commands/memoryCommand.test.ts",
    "src/ui/commands/statsCommand.test.ts",
    "src/ui/commands/terminalSetupCommand.test.ts",
    "src/ui/commands/toolsCommand.test.ts",
    "src/ui/components/ContextSummaryDisplay.test.tsx",
    "src/ui/components/Footer.test.tsx",
    "src/ui/components/InputPrompt.test.tsx",
    "src/ui/components/ModelStatsDisplay.test.tsx",
    "src/ui/components/SessionSummaryDisplay.test.tsx",
    "src/ui/components/shared/text-buffer.test.ts",
    "src/ui/components/shared/vim-buffer-actions.test.ts",
    "src/ui/components/StatsDisplay.test.tsx",
    "src/ui/components/ToolStatsDisplay.test.tsx",
    "src/ui/contexts/SessionContext.test.tsx",
    "src/ui/hooks/slashCommandProcessor.test.ts",
    "src/ui/hooks/useAtCompletion.test.ts",
    "src/ui/hooks/useConsoleMessages.test.ts",
    "src/ui/hooks/useCommandCompletion.test.ts",
    "src/ui/hooks/useFocus.test.ts",
    "src/ui/hooks/useFolderTrust.test.ts",
    "src/ui/hooks/useGeminiStream.test.tsx",
    "src/ui/hooks/useKeypress.test.ts",
    "src/ui/hooks/usePhraseCycler.test.ts",
    "src/ui/hooks/vim.test.ts",
    "src/ui/utils/computeStats.test.ts",
    "src/ui/themes/theme.test.ts",
    "src/validateNonInterActiveAuth.test.ts",
    "src/services/prompt-processors/shellProcessor.test.ts"
  ],
  "references": [{ "path": "../core" }]
}
