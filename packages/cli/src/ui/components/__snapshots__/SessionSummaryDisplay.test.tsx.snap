// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<SessionSummaryDisplay /> > renders the summary display with a title 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Agent powering down. Goodbye!                                                                   │
│                                                                                                  │
│  Interaction Summary                                                                             │
│  Session ID:                                                                                     │
│  Tool Calls:                 0 ( ✔ 0 ✖ 0 )                                                       │
│  Success Rate:               0.0%                                                                │
│  Code Changes:               +42 -15                                                             │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1h 23m 45s                                                          │
│  Agent Active:               50.2s                                                               │
│    » API Time:               50.2s (100.0%)                                                      │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
│  Model Usage                  Reqs   Input Tokens  Output Tokens                                 │
│  ───────────────────────────────────────────────────────────────                                 │
│  gemini-2.5-pro                 10          1,000          2,000                                 │
│                                                                                                  │
│  Savings Highlight: 500 (50.0%) of input tokens were served from the cache, reducing costs.      │
│                                                                                                  │
│  » Tip: For a full token breakdown, run \`/stats model\`.                                          │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;
